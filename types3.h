#ifndef TYPES_H
#define TYPES_H
#include <stdbool.h>

#define DEBUG 1


// constats

#define FLOORS 3
#define ROWS 10 // width
#define COLUMNS 25 // len
#define CELLS ROWS*COLUMNS*FLOORS
#define NUM_PLAYERS 3


#define START_AREA_ROW_START 6
#define START_AREA_ROW_END 9
#define START_AREA_COL_START 8
#define START_AREA_COL_END 16

#define F1_RECT1_COL_START 0
#define F1_RECT1_COL_END 7
#define F1_RECT2_COL_START 16
#define F1_RECT2_COL_END 24
#define F1_BRIDGE_ROW_START 6
#define F1_BRIDGE_ROW_END 9
#define F1_BRIDGE_COL_START 8
#define F1_BRIDGE_COL_END 16

#define F2_RECT_COL_START 8
#define F2_RECT_COL_END 16

// bawana cell area wall goes outwards
#define BAWANA_ROW_START 5
#define BAWANA_ROW_END 9
#define BAWANA_COL_START 21
#define BAWANA_COL_END 24
#define BAWANA_EXIT_ROW 9
#define BAWANA_EXIT_COL 19
#define BAWANA_CELLS (BAWANA_ROW_END-BAWANA_ROW_START+1)*(BAWANA_COL_END-BAWANA_COL_START+1)

#define STAIRS_CHANGE_EVERY_ROUNDS 5
#define STAIRS_DURATION 2

#define DISORIENTED_THROWS 4
#define TRIGGERED_THROWS 4

#define GAME_ROUNDS 1000


typedef enum {
    NORTH=0,
    EAST=1,
    SOUTH=2,
    WEST=3
} Direction;



typedef enum {
    NONE=0,
    NORMAL,
    START,
    BAWANA,
    WALL
} CellKind;

typedef struct {
    int row;
    int col;
    int floor;
} Coord;

typedef struct {int floor,row,col;} Position;


typedef enum {
    BI=0, UP=1, DOWN=2
} StairMode;

typedef struct {
    int s_floor, s_row, s_col, e_floor, e_row, e_col;
    StairMode mode;
} Stair;

typedef struct {
    int s_floor, e_floor, row, col;
} Pole;

typedef struct {
    CellKind kind;
    int consumable;
    int bonus_add;
    int bonus_mul;
    Coord coord;
    Stair *stair[CELLS];
    int num_stairs;
    Pole *pole;
} Cell;

typedef struct {
    char name;;
    Position position;
    Position start_cell;
    Position init_pos; // initially placed position
    Direction direction;
    Direction start_direction;
    bool in_maze;

    long long mp;
    int throws_since_dir_change;
    int turns_skipped;
    int disoriented_left;
    int triggered_left;
} Player;

typedef struct {
    int floor;
    int s_row;
    int s_col;
    int e_row;
    int e_col;
} Wall;

typedef enum {
    POINTS=0,
    FOOD_P,
    DISOR,
    TRIG,
    HAPPY,
} BawanaType;




#endif


