#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <string.h>
#include <time.h>
#include <limits.h>
#include "types.h"
#include "logic.h"

const int dirRows[4] = {-1, 0, 1, 0};  // north, east, south, west
const int dirCol[4]  = {0, 1, 0, -1};


void handle_stairs_direction(GameState *Game) {
    if (Game->game_round % STAIRS_CHANGE_EVERY_ROUNDS == 0 && Game->game_round != 0) {
        randomize_stairs_direction(Game);
        printf(" Stairs direction changed\n");
        print_stair_dir(Game);
    }
}


bool handle_food_poison(GameState *Game, Player *P, char *message) {
    if (P->turns_skipped > 0) {
        P->turns_skipped--;
        printf("%c is still food poisoned and misses the turn.\n", P->name);
        if (P->turns_skipped == 0) {
            sprintf(message, "%c is now fit to proceed from the food poisoning episode and now placed on a ", P->name);
            apply_bawana(Game, P);
        }
        return true;
    }
    return false;
}

bool handle_in_maze(GameState *Game, Player *P, int move_die) {
    if (!P->in_maze) {
        if (move_die == 6) {
            printf("%c is at the starting area and rolls 6 on the movement dice and is placed on %d of the maze.\n",
                   P->name, P->start_cell.floor * 100 + P->start_cell.row * 10 + P->start_cell.col);
            P->position = P->start_cell;
            P->in_maze = true;
            P->direction = P->start_direction;
             apply_cell_effect(P,get_cell(Game, P->start_cell.floor, P->start_cell.row,P->start_cell.col));
            P->throws_since_dir_change = (P->throws_since_dir_change + 1) % 4;
            return true;
        } else {
            printf("%c is at the starting area and rolls %d on the movement dice cannot enter the maze.\n",
                   P->name, move_die);
            return false;
        }
    }
    return true;
}

void handle_dices(GameState *Game, Player *P, int move_die, char *message) {
    bool will_roll_dir = (P->throws_since_dir_change == 3);
    int dir_face = -1;
    int steps = move_die;

    if (P->disoriented_left > 0) {
        Direction new_dir = (Direction)rand_int(0, 3);
        sprintf(message, "%c rolls and %d on the movement dice and is disoriented and move in the %s and moves %d cells and is placed at the ",
                P->name, move_die, direction_name(new_dir), move_die);
        P->direction = new_dir;
        P->disoriented_left--;
        if (P->disoriented_left == 0) {
            printf("%c has recovered from disorientation.\n", P->name);
        }
        P->throws_since_dir_change = (P->throws_since_dir_change + 1) % 4;
    } else if (will_roll_dir) {
        dir_face = rand_int(1, 6);
        int d = set_direction(dir_face);
        if (d != -1) {
            P->direction = (Direction)d;
            sprintf(message, "%c rolls and %d on the movement dice and %s on the direction dice, changes direction to %s and moves %d cells and is now at ",
                    P->name, move_die, direction_name((Direction)d), direction_name(P->direction), move_die);
        } else {
            sprintf(message, "%c rolls and %d on the movement dice and moves %s by %d cells and is now at ",
                    P->name, move_die, direction_name(P->direction), move_die);
        }
        P->throws_since_dir_change = 0;
    } else {
        sprintf(message, "%c rolls and %d on the movement dice and moves %s by %d cells and is now at ",
                P->name, move_die, direction_name(P->direction), move_die);
        P->throws_since_dir_change++;
    }


    if (P->triggered_left > 0) {
        steps *= 2;
        P->triggered_left--;
        if (will_roll_dir) {
            sprintf(message, "%c is triggered and rolls and %d on the movement dice and %d on direction die and move in the %s and moves %d cells and is placed at the ",
                    P->name, move_die, dir_face, direction_name(P->direction), steps);
        } else {
            sprintf(message, "%c is triggered and rolls and %d on the movement dice and move in the %s and moves %d cells and is placed at the ",
                    P->name, move_die, direction_name(P->direction), steps);
        }
    }
}

void move_player(GameState *Game, Player *P, int steps, char *message) {
    bool bawana = false, self_capture = false;
    Position before = P->position;
    long long mp_before = P->mp;
    int cells_moved = 0;
    long long cost = 0;

    bool move = attempt_move(Game, P, steps, &bawana, &self_capture, &cells_moved, &cost);
    
    if (self_capture) {
        P->in_maze = false;
        P->position=P->init_pos;
        P->direction=P->start_direction;
        printf("Player %c stuck on a loop and sent to start position\n", P->name);
        return; 
    } else if (!move) {
        printf("%c rolls and %d on the movement dice and cannot move in the %s. Player remains at %d\n",
               P->name, steps, direction_name(P->direction), get_cell_number(P->position));
        P->position = before;
        P->mp = mp_before;
        blocked_cost(Game, P);
    } else if (!bawana) {
        printf("%s %d.\n", message, get_cell_number(P->position));
        printf("%c moved %d that cost %lld movement points and is left with %lld and is moving in the %s.\n",
               P->name, cells_moved, cost, P->mp, direction_name(P->direction));
        bool captured = false;
        check_capture(Game, P, &captured);
    }
}

bool check_win(GameState *Game, Player *P) {
    if (P->in_maze && same_pos(P->position, Game->FLAG)) {
        printf("\n===============================================================\n");
        printf(" Player %c captured the flag at [%d,%d,%d]!\n", 
               P->name, P->position.floor, P->position.row, P->position.col);
        printf(" Game Over in %d rounds!\n", Game->game_round + 1);
        printf("===============================================================\n");
        return true;
    }
    return false;
}


void play(GameState *Game) {
    initialize_game(Game);
    bool game_over = false;
    printf("========================================\n");
    printf("||           MAZE RUNNER               ||\n");
    printf("========================================\n\n");
    printf(" Flag placed at [Floor:%d, Row:%d, Col:%d]\n\n", Game->FLAG.floor, Game->FLAG.row, Game->FLAG.col);

    for (Game->game_round = 0; Game->game_round < GAME_ROUNDS && !game_over; Game->game_round++) {
        printf("\n=========================== ROUND %d ==============================\n", Game->game_round + 1);
        
        handle_stairs_direction(Game);

        for (int player_round = 0; player_round < NUM_PLAYERS; player_round++) {
            Player *P = &Game->PLAYERS[player_round];
                char message[256] = "";
                printf("\n--------------------------------------------------------------------\n");
                printf("│ Round %3d │ Player %c │ MP: %5lld │ Pos: [%d,%d,%d] │ Dir: %s │\n",
                    Game->game_round + 1, P->name, P->mp, P->position.floor, 
                    P->position.row, P->position.col, direction_name(P->direction));
                printf("--------------------------------------------------------------------\n");


                if (handle_food_poison(Game,P, message)) {
                    continue; 
                }

                int move_die = rand_int(1, 6);
                handle_dices(Game, P,move_die,message);
                if (!handle_in_maze(Game, P, move_die)) {
                    continue;
                }

                move_player(Game, P, move_die, message);
                if (check_win(Game, P)) {
                    game_over=true;
                    if (DEBUG) print_debug_out(Game);
                    return;
                }
                
        }
    }

    if (!game_over) {
        printf("\nNo winner within %d rounds.\n", GAME_ROUNDS);
        printf(" Game ended.\n");
        if (DEBUG) print_debug_out(Game);
    }
}



void initialize_game(GameState *Game) {
    *Game = (GameState){0}; 

    load_seed();
    load_stairs(Game);
    load_poles(Game);
    load_walls(Game);

    init_cells(Game);
    init_walls(Game);
    consumables_randomize(Game);
    initialize_bawana(Game);
    initialize_stairs(Game);
    initialize_poles(Game);
    load_flag(Game);

    setup_players(Game);
}


void print_debug_out(GameState *Game) {
    if (DEBUG) {
        printf("\n\nDEBUG: total bawana visits: %d\n", Game->debug_bawana_visits);
        printf("DEBUG: stairs: %d\n", Game->debug_stairs_taken);
        printf("DEBUG: poles: %d\n", Game->debug_poles_taken);
        printf("DEBUG: wall blocks: %d\n", Game->debug_wall_blocks);
        printf("DEBUG: captures: %d\n", Game->debug_captured_players);
    }
}

void print_stair_dir(GameState *Game) {
    for (int i=0; i < Game->n_stairs; i++){
        printf("  Stair%d at [%d,%d,%d,%d,%d,%d] to %s\n",i+1,Game->STAIRS[i].s_floor, Game->STAIRS[i].s_row, Game->STAIRS[i].s_col, Game->STAIRS[i].e_floor, Game->STAIRS[i].e_row, Game->STAIRS[i].e_col, (Game->STAIRS[i].mode==UP)? "up" : (Game->STAIRS[i].mode==DOWN) ? "down":"Bi-Directional");
    }
    printf("\n");
}

// inclusive
int rand_int(int low, int high) {
    return low + (rand() % (high-low +1));
}

// returns true if direction valid

bool is_valid_direction(int d) {
    return d>=0 && d<=3;
}

char* direction_name(Direction dir) {
    if (!is_valid_direction(dir)) return "Not Valid";
    switch (dir) {
        case NORTH: return "NORTH";
        case EAST: return "EAST";
        case WEST: return "WEST";
        case SOUTH: return "SOUTH";
    }
}

// set direction
int set_direction(int die) {
    switch(die) {
        case 2: return NORTH;
        case 3: return EAST;
        case 4: return SOUTH;
        case 5: return WEST;
        default: return -1;
    }
}

char *get_bawana_type_name(BawanaType type){
    switch (type)
            {
            case FOOD_P:
                return "bawana food poisoning";
            case POINTS:
                return "bawana normal cell";
            case DISOR:
                return "bawana disoriented cell";
            case TRIG:
                return "bawana triggered cell";
            case HAPPY:
                return "bawana happy cell";
            default:
                return "bawana cell";
            }
}


/*
returns cell pointer taking floor, row, column
*/
Cell* get_cell (GameState *Game, int floor, int row, int col) {
    if (!is_in_board(row, col)) return NULL;
    return &Game->BOARD[floor][row][col];
}

/*
returns true if the cell is within the board (not considering the restricted areas, only outer box)
*/
bool is_in_board (int row, int col) {
    return (row >= 0 && row < ROWS && col >= 0 && col < COLUMNS);
}

bool is_cell_available(GameState *Game, int floor, int row, int col) {
    if (!is_in_board(row, col)) return false;
    Cell *cell = get_cell(Game, floor, row, col);
    if (cell->kind==NORMAL) return true;
    return false;
}

bool is_cell_available_for_poles(GameState *Game, int floor, int row, int col) {
    if (!is_in_board(row, col)) return false;
    Cell *cell = get_cell(Game, floor, row, col);
    if (cell->kind==NORMAL || cell->kind==START) return true;
    return false;
}


void set_cell (Cell *cell, CellKind kind, int consumables, int add, int mul) {
    cell->kind = kind;
    cell->consumable = consumables;
    cell->bonus_add = add;
    cell->bonus_mul = mul;
    for (int i = 0; i< CELLS; i++){
        cell->stair[i] = NULL;
    }
    cell->num_stairs=0;
    cell->pole = NULL;
}

void init_cells(GameState *Game){
    for (int f=0; f<FLOORS; f++) {
        for (int r = 0; r < ROWS; r++){
            for (int c=0; c < COLUMNS; c++){
                Cell *cell = &Game->BOARD[f][r][c];
                Coord coordinates;
                coordinates.col =c;
                coordinates.row = r;
                coordinates.floor = f;
                cell->coord = coordinates;
                if (f==0){
                    if(r>=START_AREA_ROW_START && r <= START_AREA_ROW_END && c >= START_AREA_COL_START && c<=START_AREA_COL_END){
                        set_cell(cell, START, 0, 0, 1);
                        continue;
                    }
                    if (r>=BAWANA_ROW_START && r <= BAWANA_ROW_END && c >= BAWANA_COL_START && c <= BAWANA_COL_END){
                        set_cell(cell, BAWANA, 0, 0, 1);
                        continue;
                    }
                    if (r == BAWANA_ROW_START-1 && c >= BAWANA_COL_START-1 && c <= BAWANA_COL_END) {
                        set_cell(cell, WALL, 0, 0, 1);
                        continue;
                    }
                    if (r >= BAWANA_ROW_START && r <= BAWANA_ROW_END &&  c == BAWANA_COL_START-1) {
                        set_cell(cell, WALL, 0, 0, 1);
                        continue;
                    }
                }
                if (f==1){
                    bool in_left   = (c >= F1_RECT1_COL_START && c <= F1_RECT1_COL_END);          // 0..7
                    bool in_right  = (c >= F1_RECT2_COL_START && c <= F1_RECT2_COL_END);    // 16..24
                    bool in_bridge = (r >= F1_BRIDGE_ROW_START && r <= F1_BRIDGE_ROW_END &&
                                    c >= F1_BRIDGE_COL_START && c <= F1_BRIDGE_COL_END);        // 6..9, 8..16
                    if (!(in_left || in_right || in_bridge)) {
                        set_cell(cell, NONE, 0, 0, 1);
                        continue;
                    }
                }

                if (f==2){
                    if(!(c >= F2_RECT_COL_START && c<=F2_RECT_COL_END)){
                        set_cell(cell,NONE,0,0,1);
                        continue;
                    }
                }
                set_cell(cell,NORMAL, 0, 0, 1);
            }
        }
    }
}

int min(int a, int b) {
    if (a>b) return b;
    return a;
}

int max(int a, int b) {
    if (a>b) return a;
    return b;
}

void add_wall(GameState *Game, int floor, int row1,  int col1, int row2, int col2) {
    if (!(is_in_board(row1, col1)) || !(is_in_board(row2,col2))) return;
    if (!((row1==row2) || (col1==col2))) return;
    if (row1==row2){
        for (int i = min(col1,col2); i <= max(col1, col2); i++){
            Game->BOARD[floor][row1][i].kind = WALL;
        }
    }
    if (col1==col2){
        for (int i = min(row1,row2); i <= max(row1, row2); i++){
            Game->BOARD[floor][i][col1].kind = WALL;
        }
    }
}

void init_walls(GameState *Game){
    for (int i=0; i < Game->n_walls; i++){
        add_wall(Game, Game->WALLS[i].floor,Game->WALLS[i].s_row,Game->WALLS[i].s_col, Game->WALLS[i].e_row, Game->WALLS[i].e_col);
    }
}

void consumables_randomize(GameState *Game){
    Cell *cells[CELLS];
    int n_active = 0;
    for (int f=0; f < FLOORS; f++) {
        for (int r=0; r < ROWS; r++){
            for (int c=0; c < COLUMNS; c++) {
                if (!(is_cell_available(Game, f,r,c))) continue;
                cells[n_active++] = &Game->BOARD[f][r][c];
            }
        }
    }

    int con_none = (int) (0.25*n_active);
    int con_1 = (int) (0.35*n_active);
    int bonus_12 = (int) (0.25*n_active);
    int bonus_35 = (int) (0.10*n_active);
    int mul_23 = n_active - (con_none + con_1 + bonus_12 + bonus_35);


    int added_cells =0;
    #define EMPTY(random_cell) ((random_cell)->consumable == -1 && (random_cell)->bonus_add == 0 && (random_cell)->bonus_mul == 1)
    // consumables 0
    int i = 0;
    while (i < con_none && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->consumable =0;
        i++, added_cells++; 
    }
    // consumable 1-4
    i=0;

    while (i < con_1 && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->consumable = rand_int(1,4);
        if (DEBUG) printf("DEBUG: Cell [%d,%d,%d] - consumable %d\n", random_cell->coord.floor, random_cell->coord.row, random_cell->coord.col, random_cell->consumable);
        i++, added_cells++; 
    }
    
    // bonus 1,2
    i=0;
    while (i < bonus_12 && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->bonus_add = rand_int(1,2);
        if (DEBUG) printf("DEBUG: Cell [%d,%d,%d] - bonus %d\n", random_cell->coord.floor, random_cell->coord.row, random_cell->coord.col, random_cell->bonus_add);
        i++, added_cells++; 
    }

    // bonus 3..5
    i=0;
    while (i < bonus_35 && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->bonus_add = rand_int(3,5);
        if (DEBUG) printf("DEBUG: Cell [%d,%d,%d] - bonus %d\n", random_cell->coord.floor, random_cell->coord.row, random_cell->coord.col, random_cell->bonus_add);
        i++, added_cells++; 
    }
    // mul 2,3
    i=0;
    while (i < mul_23 && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->bonus_mul = rand_int(2,3);
        if (DEBUG) printf("DEBUG: Cell [%d,%d,%d] - bonus multiplier %d\n", random_cell->coord.floor, random_cell->coord.row, random_cell->coord.col, random_cell->bonus_mul);
        i++, added_cells++; 
    }

}

// bawana

void initialize_bawana(GameState *Game) {
    BawanaType bw_cells[BAWANA_CELLS];
    int food_p = 2, trig = 2, disor=2, happy=2, points=4;
    int added_cells =0;
    for (int i=0; i < BAWANA_CELLS; i++) {
        bw_cells[i] = POINTS;
    }
    // food poisoning
    int i = 0;
    while (i < food_p && added_cells < BAWANA_CELLS){
        BawanaType *random_cell = &bw_cells[rand_int(0, BAWANA_CELLS-1)];
        if (*random_cell!=POINTS) continue;
        *random_cell=FOOD_P;
        i++, added_cells++;
    }
    // disoreadted
    i = 0;
    while (i < trig && added_cells < BAWANA_CELLS){
        BawanaType *random_cell = &bw_cells[rand_int(0, BAWANA_CELLS-1)];
        if (*random_cell!=POINTS) continue;
        *random_cell=TRIG;
        i++, added_cells++;
    }
    // happy
    i = 0;
    while (i < happy && added_cells < BAWANA_CELLS){
        BawanaType *random_cell = &bw_cells[rand_int(0, BAWANA_CELLS-1)];
        if (*random_cell!=POINTS) continue;
        *random_cell=HAPPY;
        i++, added_cells++;
    }

    added_cells=0;
    for (int row=0; row<=BAWANA_ROW_END-BAWANA_ROW_START; row++) {
        for (int col=0; col<=BAWANA_COL_END-BAWANA_COL_START; col++){
            Game->BAWANA_MAP[row][col] = bw_cells[added_cells++];
        }
    }

}

BawanaType random_bawana_cell(GameState *Game) {
    int ran_col = rand_int(0, BAWANA_COL_END-BAWANA_COL_START);
    int ran_row = rand_int(0, BAWANA_ROW_END-BAWANA_ROW_START);
    return Game->BAWANA_MAP[ran_row][ran_col];
}

void exit_bawana(Player *p) {
    p->position= (Position) {0, BAWANA_EXIT_ROW, BAWANA_EXIT_COL};
    p->direction=NORTH;
}


void apply_bawana(GameState *Game, Player *p) {
    Game->debug_bawana_visits++;
    BawanaType type = random_bawana_cell(Game);
    printf("%c is place on a %s and effects take place.\n", p->name, get_bawana_type_name(type));
    if (type==FOOD_P) {
        p->turns_skipped=3;
        printf("%c eats from Bawana and have a bad case of food poisoning. Will need three rounds to recover.\n", p->name);
    } else if (type==DISOR) {
        p->mp += 50;
        p->disoriented_left = DISORIENTED_THROWS;
        exit_bawana(p);
        printf("%c eats from Bawana and is disoriented and is placed at the entrance of Bawana with 50 movement points.\n", p->name);
    } else if (type==TRIG) {
        p->mp += 50;
        p->triggered_left = TRIGGERED_THROWS;
        exit_bawana(p);
        printf("%c eats from Bawana and is triggered due to bad quality of food. %c is placed at the entrance of Bawana with 50 movement points.\n", p->name, p->name);
    } else if (type==HAPPY) {
        p->mp += 200;
        exit_bawana(p);
        printf("%c eats from Bawana and is happy. %c is placed at the entrance of Bawana with 200 movement points.\n", p->name, p->name);
    } else {
        long long pts = rand_int(10,100);
        p->mp += pts;
        exit_bawana(p);
        printf("%c eats from Bawana and earns %lld movement points and is placed at the %d.\n", p->name, pts, get_cell_number(p->position));
    }

}

void randomize_stairs_direction(GameState *Game) {
    for (int i=0; i < Game->n_stairs; i++) {
        int random_012 = rand_int(0,2); // use bi direction too
        Game->STAIRS[i].mode = random_012;
    }

}

void stairs_bidirectional(GameState *Game) {
    for (int i=0; i < Game->n_stairs; i++) {
        Game->STAIRS[i].mode = BI;
    }
}

void initialize_stairs(GameState *Game) {
    for (int i = 0; i < Game->n_stairs; i++) {
        Stair *stair = &Game->STAIRS[i];
        if (!(is_cell_available(Game, stair->e_floor, stair->e_row, stair->e_col) && is_cell_available(Game, stair->s_floor, stair->s_row, stair->s_col))) {
            if (DEBUG) printf("DEBUG: Stair [%d,%d,%d,%d,%d,%d] skipped\n", stair->s_floor,stair->s_row,stair->s_col,stair->e_floor,stair->e_row,stair->e_col);
            continue;
        }
        Cell *start_cell = get_cell(Game, stair->s_floor, stair->s_row, stair->s_col);
        if (start_cell != NULL) {
            if (start_cell->num_stairs>=2) continue;
            start_cell->stair[start_cell->num_stairs++] = stair;
        }
        if (!(stair->s_floor == stair->e_floor && stair->s_row == stair->e_row && stair->s_col == stair->e_col)) {
            Cell *end_cell = get_cell(Game, stair->e_floor, stair->e_row, stair->e_col);
            if (end_cell != NULL) {
                end_cell->stair[end_cell->num_stairs++] = stair;
            }
        }
        block_stair_passing_cells(Game, stair);
        if (DEBUG) printf("DEBUG: Stair [%d,%d,%d,%d,%d,%d] added\n", stair->s_floor,stair->s_row,stair->s_col,stair->e_floor,stair->e_row,stair->e_col);
    }

    stairs_bidirectional(Game);
    //stairs_up();
}



void initialize_poles(GameState *Game) {
    for (int i = 0; i < Game->n_poles; i++) {
        Pole *pole = &Game->POLES[i];
        bool valid = true;
        for (int floor = pole->s_floor; floor <= pole->e_floor; floor++) {
            Cell *cell = get_cell(Game, floor, pole->row, pole->col);
            if (!is_cell_available_for_poles(Game, cell->coord.floor,cell->coord.row,cell->coord.col)){
                valid = false;
                if (DEBUG) printf("DEBUG: Pole [%d,%d,%d,%d] not valid skipped\n", pole->s_floor, pole->e_floor, pole->row, pole->col);
                break;
            }
        }
        if (!valid) continue;
        for (int floor = pole->s_floor; floor <= pole->e_floor; floor++) {
            Cell *cell = get_cell(Game, floor, pole->row, pole->col);
            if (cell != NULL) {
                if (cell->pole != NULL) {
                    if (pole->s_floor <= cell->pole->s_floor && pole->e_floor >= cell->pole->e_floor){
                        cell->pole = pole;
                    }

                } else {
                    cell->pole = pole;
                }
            }
        }
        if (DEBUG) printf("DEBUG: Pole [%d,%d,%d,%d] added\n", pole->s_floor, pole->e_floor, pole->row, pole->col);

    }
}


int man_best_distance(Position pos1, Position pos2) {
    return (abs(pos1.floor-pos2.floor)+1) * (abs(pos1.row - pos2.row) + abs(pos1.col - pos2.col));
}

bool can_use_stair(Stair *stair, Position current_pos) {
    

    //  start position
    if (current_pos.floor == stair->s_floor &&
        current_pos.row == stair->s_row &&
        current_pos.col == stair->s_col) {
        return (stair->mode == BI || stair->mode == UP);
    }

    // end position
    if (current_pos.floor == stair->e_floor &&
        current_pos.row == stair->e_row &&
        current_pos.col == stair->e_col) {
        return (stair->mode == BI || stair->mode == DOWN);
    }

    return false;
}

Cell *check_stairs(GameState *Game, Cell *current) {
    if (current->num_stairs == 0) return NULL;
    Position current_pos = {current->coord.floor, current->coord.row, current->coord.col};

    if (DEBUG) printf("DEBUG:[%d,%d,%d] has %d stairs\n",
                      current_pos.floor, current_pos.row, current_pos.col, current->num_stairs);

    Cell *best_destination = NULL;
    int best_distance = INT_MAX;


    for (int i = 0; i < current->num_stairs; i++) {
        Stair *stair = current->stair[i];

        if (DEBUG) printf("DEBUG:  Stair %d: [%d,%d,%d] to [%d,%d,%d], mode=%d\n", i,
                          stair->s_floor, stair->s_row, stair->s_col,
                          stair->e_floor, stair->e_row, stair->e_col,
                          stair->mode);

        if (!can_use_stair(stair, current_pos)) {
            if (DEBUG) printf("DEBUG:cant use %d stair\n", i);
            continue;
        }

        Position dest_pos;
        if (current_pos.floor == stair->s_floor &&
            current_pos.row == stair->s_row &&
            current_pos.col == stair->s_col) {
            // start to end
            dest_pos = (Position){stair->e_floor, stair->e_row, stair->e_col};
            if (DEBUG) printf("DEBUG: stair st to end [%d,%d,%d]\n",
                              dest_pos.floor, dest_pos.row, dest_pos.col);
        } else if (current_pos.floor == stair->e_floor &&
                   current_pos.row == stair->e_row &&
                   current_pos.col == stair->e_col) {
            //at end to start
            dest_pos = (Position){stair->s_floor, stair->s_row, stair->s_col};
            if (DEBUG) printf("DEBUG:stair end to start [%d,%d,%d]\n",
                              dest_pos.floor, dest_pos.row, dest_pos.col);
        } else {
            if (DEBUG) printf("DEBUG:not at either end");
        }

        // stairs lead to samepos
        if (dest_pos.floor == current_pos.floor &&
            dest_pos.row == current_pos.row &&
            dest_pos.col == current_pos.col) {
            continue;
        }

        Cell *dest_cell = get_cell(Game, dest_pos.floor, dest_pos.row, dest_pos.col);
        if (dest_cell == NULL || !is_cell_available(Game, dest_pos.floor, dest_pos.row, dest_pos.col)) {
            continue;
        }

        int distance = man_best_distance(dest_pos, Game->FLAG);


        if (best_destination == NULL || distance < best_distance) {
            best_destination = dest_cell;
            best_distance = distance;
            if (DEBUG) printf("DEBUG:best dest  [%d,%d,%d] (dist %d)\n",
                              dest_pos.floor, dest_pos.row, dest_pos.col, distance);
        }
    }

    if (DEBUG) {
        if (best_destination != NULL) {
            printf("DEBUG: stair final [%d,%d,%d] to [%d,%d,%d]\n",
                   current_pos.floor, current_pos.row, current_pos.col,
                   best_destination->coord.floor, best_destination->coord.row, best_destination->coord.col);
        } else {
            printf("DEBUG: no usable stair at [%d,%d,%d]\n",
                   current_pos.floor, current_pos.row, current_pos.col);
        }
    }

    return best_destination;
}


// poles
Cell *check_pole(GameState *Game, Cell *current) {
    if (!current->pole) return NULL;
    if (current->coord.floor > current->pole->s_floor) {
        return get_cell(Game, current->pole->s_floor, current->pole->row, current->pole->col);
    }
    return NULL;
}
// movement
void apply_cell_effect(Player *p, const Cell *cell){
    if (cell->consumable > 0) {
        p->mp -= cell->consumable;
        if (DEBUG) printf("DEBUG: %i MP lost(total: %lli)\n", cell->consumable,p->mp);
    }
    else if (cell->bonus_add > 0) {
        p->mp += cell->bonus_add;
        if (DEBUG) printf("DEBUG: %i MP gain(total: %lli)\n", cell->bonus_add,p->mp);
        
    }
    else if (cell->bonus_mul > 1){
        p->mp *= cell->bonus_mul;
        if (DEBUG) printf("DEBUG: x%i MP multiply(total: %lli)\n", cell->bonus_mul,p->mp);
        
    } 
} 



void check_capture(GameState *Game, Player* P, bool *captured) {
    *captured = false;
    for (int i = 0; i < NUM_PLAYERS; i++) {
        Player *Q = &Game->PLAYERS[i];
        if (Q == P) continue;
        if (!Q->in_maze) continue;
        if (P->position.floor==Q->position.floor && P->position.row==Q->position.row && P->position.col == Q->position.col) {
            *captured = true;
            printf("Player %c captures player %c and sent to start position\n", P->name, Q->name);
            Q->in_maze=false;
            Q->direction=Q->start_direction;
            Q->position=Q->init_pos;
            Game->debug_captured_players++;
        }
    }
}

bool next_cell_check(GameState *Game, Position next, Player *P){
        if (!is_cell_available(Game, next.floor,next.row, next.col) || !is_valid_direction(P->direction)) {
            CellKind blocked_cell_kind;
            char *block_type= "due to cell not in maze";
            if (is_in_board(next.row, next.col)) {
                blocked_cell_kind = get_cell(Game, next.floor, next.row, next.col)->kind;
                switch (blocked_cell_kind)
                {
                    case WALL:
                        Game->debug_wall_blocks++;
                        block_type = "by a Wall";
                        break;
                    case START:
                        block_type = "by the starting area";
                        break;
                    case BAWANA:
                        block_type = "by the bawana area";
                        break;
                    default:
                        break;
                }
            }
            if (DEBUG) printf("DEBUG: Move blocked in [%d,%d,%d] %s\n", next.floor, next.row, next.col, block_type);
            return false;
        }
        return true;
}

void apply_pole_or_stair(GameState *Game, Position *current, Player *P) {
    Cell *cell_c = get_cell(Game, current->floor, current->row, current->col);
    Cell *jumped = check_pole(Game, cell_c); // prioritize pole
    if (jumped) {
        Game->debug_poles_taken++;
        printf("%c lands on %d which is a pole cell. %c slides down and now placed at %d in floor %d\n",
                P->name, get_cell_number((Position){current->floor, current->row, current->col}),
                P->name, get_cell_number((Position){jumped->coord.floor, jumped->coord.row, jumped->coord.col}),
                jumped->coord.floor);
        if (jumped->kind==START){
            P->in_maze=false;
            P->position=P->init_pos;
            P->direction=P->start_direction;
            printf("%c sent to starting position because %c took a pole to starting area\n", P->name, P->name);
            return;
        }

        *current = (Position) {jumped->coord.floor, jumped->coord.row, jumped->coord.col};
    } else {
        jumped = check_stairs(Game, cell_c);
        if (jumped) {
            Game->debug_stairs_taken++;
            printf("%c lands on %d which is a stair cell. %c takes the stairs and now placed at %d in floor %d\n",
                    P->name, get_cell_number((Position){current->floor, current->row, current->col}),
                    P->name, get_cell_number((Position){jumped->coord.floor, jumped->coord.row, jumped->coord.col}),
                    jumped->coord.floor);

            *current = (Position) {jumped->coord.floor, jumped->coord.row, jumped->coord.col};
        }
    }
}

bool attempt_move(GameState *Game, Player *P, int steps, bool *sent_to_bawana, bool *self_capture, int *cells_moved, long long *cost) {
    *sent_to_bawana = false;
    *self_capture = false;
    *cells_moved = 0;
    *cost = 0;
    Position start = P->position;
    long long mp_start = P->mp;
    Position current = start;
    int remaining = steps;

    while (remaining > 0) {
        Position next = (Position) {
            current.floor,
            current.row + dirRows[P->direction],
            current.col + dirCol[P->direction]
        };
        if (!next_cell_check(Game, current, P) || !next_cell_check(Game, next, P)){
            P->position = start;
            P->mp = mp_start;
            *cells_moved=0;
            *cost=-2;
            return false;
        }
        current = next;

        apply_pole_or_stair(Game, &current, P);

        long long mp_before_cell = P->mp;
        apply_cell_effect(P, get_cell(Game, current.floor, current.row, current.col));
        long long mp_after_cell = P->mp;
        if (mp_after_cell < mp_before_cell) {
            *cost += (mp_before_cell - mp_after_cell);
        }
        (*cells_moved)++;

        if (P->mp <= 0) {
            printf("%c movement points are depleted and requires replenishment. Transporting to Bawana.\n", P->name);
            P->position = current;
            apply_bawana(Game, P);
            *sent_to_bawana=true;
            return true;
        }
        remaining--;

    }
    // self loop check
    if (same_pos(current, start)) {
        *self_capture = true;
        return false;
    }
    P->position = current;
    return true;
}

void blocked_cost(GameState *Game, Player *P) {
    P->mp -= 2;
    printf("%c moved 0 that cost 2 movement points and is left with %lld and is moving in the %s.\n",
           P->name, P->mp, direction_name(P->direction));
    if (P->mp <= 0) {
        printf("%c movement points are depleted and requires replenishment. Transporting to Bawana.\n", P->name);
        apply_bawana(Game, P);
    }
}


// init

void place_flag_randomly(GameState *Game) {
    while (true) {
        int floor = rand_int(0,2), row = rand_int(0,9), col = rand_int(0,24);
        if (!is_cell_available(Game, floor,row,col)) continue;
        Cell *cell = get_cell(Game, floor,row, col);
        if (cell->pole || cell->num_stairs > 0) continue;
        Game->FLAG = (Position){floor,row,col};
        break;
    }
}

void initialize_player(Player *player, char name, int start_row, int start_col, int first_row, int first_col, Direction dir){
    player->name = name;
    player->position = (Position) {0,start_row,start_col};
    player->init_pos = (Position) {0,start_row,start_col};
    player->start_cell = (Position) {0, first_row, first_col};
    player->direction = dir;
    player->start_direction = dir;
    player->in_maze = false;
    player->mp = 100;
    player->throws_since_dir_change=0;
    player->turns_skipped=0;
    player->disoriented_left=0;
    player->triggered_left=0;
}

void setup_players(GameState *Game) {

    initialize_player(&Game->PLAYERS[0],'A',6,12,5,12,NORTH);
    initialize_player(&Game->PLAYERS[1],'B',9,8,9,7,WEST);
    initialize_player(&Game->PLAYERS[2],'C',9,16,9,17,EAST);

}

bool same_pos(Position a, Position b) {
    return (a.floor==b.floor && a.row==b.row && a.col==b.col);
}

// load stairs from file
void load_stairs(GameState *Game) {
    FILE *file = fopen("stairs.txt", "r");
    if (!file) { printf("Failed to open stairs.txt\n"); exit(1); }

    Game->n_stairs = 0;
    char line[256];
    while (fgets(line, sizeof line, file) && Game->n_stairs < CELLS) {
        int s_floor, s_row, s_col, e_floor, e_row, e_col;
        if (sscanf(line, " [ %d , %d , %d , %d , %d , %d ] ",
                   &s_floor, &s_row, &s_col, &e_floor, &e_row, &e_col) == 6) {
            Game->STAIRS[Game->n_stairs].s_floor = s_floor;
            Game->STAIRS[Game->n_stairs].s_row   = s_row;
            Game->STAIRS[Game->n_stairs].s_col   = s_col;
            Game->STAIRS[Game->n_stairs].e_floor = e_floor;
            Game->STAIRS[Game->n_stairs].e_row   = e_row;
            Game->STAIRS[Game->n_stairs].e_col   = e_col;
            Game->STAIRS[Game->n_stairs].mode = BI;
            Game->n_stairs++;
        }
    }
    fclose(file);
    printf("Loaded %d stairs\n", Game->n_stairs);
}

void load_poles(GameState *Game) {
    FILE *file = fopen("poles.txt", "r");
    if (!file) { printf("failed to open poles.txt\n"); exit(1); }

    Game->n_poles = 0;
    char line[256];
    while (fgets(line, sizeof line, file) && Game->n_poles < CELLS) {
        int s_floor, s_row, s_col, e_floor;
        if (sscanf(line, " [ %d , %d , %d , %d ] ",
                   &s_floor, &e_floor, &s_row, &s_col) == 4) {
            Game->POLES[Game->n_poles].s_floor = s_floor;
            Game->POLES[Game->n_poles].row     = s_row;
            Game->POLES[Game->n_poles].col     = s_col;
            Game->POLES[Game->n_poles].e_floor = e_floor;
            Game->n_poles++;
        }
    }
    fclose(file);
    printf("Loaded %d poles\n", Game->n_poles);
}

void load_walls(GameState *Game) {
    FILE *file = fopen("walls.txt", "r");
    if (!file) { printf("Error: Could not open walls.txt\n"); exit(1); }

    Game->n_walls = 0;
    char line[256];
    while (fgets(line, sizeof line, file) && Game->n_walls < CELLS) {
        int floor, s_row, s_col, e_row, e_col;
        if (sscanf(line, " [ %d , %d , %d , %d , %d ] ",
                   &floor, &s_row, &s_col, &e_row, &e_col) == 5) {
            Game->WALLS[Game->n_walls].floor = floor;
            Game->WALLS[Game->n_walls].s_row = s_row;
            Game->WALLS[Game->n_walls].s_col = s_col;
            Game->WALLS[Game->n_walls].e_row = e_row;
            Game->WALLS[Game->n_walls].e_col = e_col;
            Game->n_walls++;
        }
    }
    fclose(file);
    printf("Loaded %d walls\n", Game->n_walls);
}
void load_flag(GameState *Game) {
    FILE *file = fopen("flag.txt", "r");
    if (!file) {
        printf("Cant open flag.txt\n");
        place_flag_randomly(Game);
        printf("Flag Placed Randomly\n");
        return;
    }

    char line[256];
    if (fgets(line, sizeof line, file)) {
        int floor, row, col;
        if (sscanf(line, " [ %d , %d , %d ] ", &floor, &row, &col) == 3) {
            if (is_cell_available(Game, floor, row, col)) {
                Game->FLAG.floor = floor;
                Game->FLAG.row   = row;
                Game->FLAG.col   = col;
                printf("flag loaded from flag.txt: [%d,%d,%d]\n", floor, row, col);
                fclose(file);
                return;
            }
        }
    }
    fclose(file);
    place_flag_randomly(Game);
    printf("can't get flag position, Flag Placed Randomly\n");
}

int get_cell_number(Position pos) {
    return pos.floor * 100 + pos.row * 10 + pos.col;
}

// since only 3 floors
void block_stair_passing_cells(GameState *Game, Stair *stair) {
    int dif_floors = abs(stair->e_floor-stair->s_floor);
    if (dif_floors <= 1) return;
    int diff_row = abs(stair->e_row-stair->s_row);
    int diff_col = abs(stair->e_col-stair->s_col);
    int row = min(stair->e_row,stair->s_row)+(diff_row)/2;
    int col = min(stair->e_col,stair->s_col)+(diff_col)/2;
    add_wall(Game, 1,row,col,row,col);
}
void load_seed(){
    FILE *file = fopen("seed.txt","r");
    unsigned int seed;
    if (!file){
        printf("Can't open seed.txt, using random seed\n");
        srand((unsigned)time(NULL));
        return;
    }
    if(fscanf(file, "%u", &seed) == 1) {
        srand(seed);
        printf("seed %u\n", seed);
        fclose(file);
        return;
    }
    fclose(file);
    srand((unsigned)time(NULL));
    printf("Using random seed\n");
}
